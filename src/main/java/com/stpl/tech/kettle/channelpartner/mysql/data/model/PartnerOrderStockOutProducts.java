package com.stpl.tech.kettle.channelpartner.mysql.data.model;


import lombok.Data;
import org.joda.time.DateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.Set;

@Data
@Entity
@Table(name = "PARTNER_ORDER_STOCK_OUT_PRODUCTS")
public class PartnerOrderStockOutProducts {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FALLBACK_STATUS_ID", nullable = false)
    private PartnerOrderFallbackStatus partnerOrderFallbackStatusId;

    @Column(name = "PRODUCT_ID", nullable = false, length = 500)
    private Integer productId;

    @Column(name = "DIMENSION" , nullable = false, length = 500)
    private String dimension;

    @Column(name = "STOCK_REQUEST_COUNT" , nullable = false, length = 500)
    private Integer stockRequestCount;

    @Column(name = "STOCK_AVAILABLE_COUNT" , nullable = false, length = 500)
    private Integer stockAvailableCount;

    @Column(name = "STOCK_OUT_TIME" , nullable = false, length = 500)
    private Date time;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "partnerOrderFallbackStatusId")
    private Set<PartnerOrderFallbackLog> partnerOrderFallbackLogs;

}
