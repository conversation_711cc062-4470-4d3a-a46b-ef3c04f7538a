package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCafeStatusResponse;
import com.stpl.tech.master.domain.model.UnitStatus;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "SwiggyCafeStatusData")
@Data
public class SwiggyCafeStatusData {

    @Id
    private String id;
    private String name;
    private Integer unitId;
    private Integer brandId;
    private Boolean partnerStatus;
    private String city;
    private String region;
    private Boolean isCafeLive;
    private UnitStatus unitStatus;
    private SwiggyCafeStatusResponse swiggyResponse;
    private String lastUpdatedTime;
    private boolean updationRequest=false;
    private Date lastSyncTime;
    private Date lastUpdatedTimeIST;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Boolean getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(Boolean partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public Boolean getCafeLive() {
        return isCafeLive;
    }

    public void setCafeLive(Boolean cafeLive) {
        isCafeLive = cafeLive;
    }

    public void setUnitStatus(UnitStatus unitStatus) {
        this.unitStatus = unitStatus;
    }

    public UnitStatus getUnitStatus() {
        return unitStatus;
    }

    public SwiggyCafeStatusResponse getSwiggyResponse() {
        return swiggyResponse;
    }

    public void setSwiggyResponse(SwiggyCafeStatusResponse swiggyResponse) {
        this.swiggyResponse = swiggyResponse;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(String lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public boolean isUpdationRequest() {
        return updationRequest;
    }

    public void setUpdationRequest(boolean updationRequest) {
        this.updationRequest = updationRequest;
    }

    public Date getLastSyncTime() {
        return this.lastSyncTime;
    }

    public void setLastSyncTime(Date lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }
}
