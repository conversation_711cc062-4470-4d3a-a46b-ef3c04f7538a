package com.stpl.tech.kettle.channelpartner.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackLog;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.OrderAttribute;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import lombok.Builder;

public class ChannelPartnerUtils extends AppUtils {

    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerUtils.class);
    private static final String NO_SUGAR = "No Sugar";
    public static final String SWIGGY = "Swiggy";
    public static final String ZOMATO = "Zomato";
    public static final String PARTNER_FACTORY_SERVICE = "FactoryService";
    public static final String EVENT_FACTORY_SERVICE = "EventFactoryService";

    public static final String PRE_PROCESSOR_SERVICE = "PreProcessorLayer";

    public static final String EVENT_PROCESSOR_SERVICE = "EventProcessor";

    public static final String REQ_PROCESSOR_SERVICE = "ReqToPartner";

    public static  final String SWIGGY_BOLT_TAG = "Bolt-10 min delivery";




    public static String generatePartnerOrderId(String partnerShortCode) {
        return partnerShortCode + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(getCurrentTimestamp());
    }

    public static BigDecimal percentageOfWithScale10(BigDecimal percent, BigDecimal value) {
        return multiplyWithScale10(divideWithScale(percent, BigDecimal.valueOf(100), 10), value);
    }

    public static <T> T deserializeObject(String data, Class<T> clazz) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(data, clazz);
        } catch (IOException e) {
            LOG.error("Error de-serializing data", e);
        }
        return null;
    }

    public static <T> List<T> deserializeList(String data, TypeReference<List<T>> typeReference) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(data, typeReference);
        } catch (IOException e) {
            LOG.error("Error de-serializing data", e);
        }
        return null;
    }

    public static String getMessage ( String title , String text ){
        StringBuilder sb = new StringBuilder();
        sb.append(" ################### "+title+" ################### ");
        sb.append("\n");
        sb.append(text);
        sb.append("\n");
        sb.append("-");
        sb.append("\n");
        return sb.toString();
    }

    public static String getMessage ( String title , Exception e ){
        return ChannelPartnerUtils.getMessage(title,e.getMessage());
    }

    public static Map<String, Integer> getDesiChaiMilkMap() {
        Map<String, Integer> map = new HashMap<>();
        map.put("Regular Milk", 10);
        map.put("Full Doodh (Full Milk)", 11);
        map.put("Doodh Kum (Less Milk)", 12);
        map.put("Paani Kum (More Milk)", 50);
        map.put("Full Doodh", 11);
        map.put("Doodh Kum", 12);
        map.put("Paani Kum", 50);
        return map;
    }

    public static Map<String, Integer> getBaarishWaliChaiMilkMap() {
        Map<String, Integer> map = new HashMap<>();
        map.put("Regular Milk", 1282);
        map.put("Full Doodh (Full Milk)", 1292);
        map.put("Doodh Kum (Less Milk)", 1294);
        map.put("Paani Kum (More Milk)", 1293);
        map.put("Full Doodh", 1292);
        map.put("Doodh Kum", 1294);
        map.put("Paani Kum", 1293);
        return map;
    }

    public static Map<Integer, Integer> getDesiChaiVariantMap() {
        Map<Integer, Integer> map = new HashMap<>();
        map.put(10, 10);
        map.put(11, 10);
        map.put(12, 10);
        map.put(50, 10);
        map.put(1282, 1282);
        map.put(1292, 1282);
        map.put(1294, 1282);
        map.put(1293, 1282);
        return map;
    }

    public static Map<Integer, String> getDesiChaiProductName() {
        Map<Integer, String> map = new HashMap<>();
        map.put(10, "Desi Chai");
        map.put(11, "Desi Full Doodh");
        map.put(12, "Desi Doodh Kum");
        map.put(50, "Desi Paani Kum");
        map.put(1282, "Lemon Grass Chai");
        map.put(1292, "Lemon Grass Chai Full Doodh");
        map.put(1294, "Lemon Grass Chai Doodh Kum");
        map.put(1293, "Lemon Grass Chai Paani Kum");
        return map;
    }

    public static List<String> getQVMDefaultAddons() {
        List<String> addons = new ArrayList<>();
        addons.add("Tulsi");
        addons.add("Adrak");
        return addons;
    }

    public static Date getCurrentTimestamp() {
        return getCurrentTimeIST().toLocalDateTime().toDate();
    }

    public static Pair<String, String> parseUnitId(String outletId) {
        String[] splits = outletId.split("-");
        if (splits.length == 1) {
            return new Pair<>(outletId, null);
        } else if (splits.length >= 2) {
            String value = null;
            try {
                value = OrderAttribute.valueOf(splits[1]).getCode();
            } catch (Exception e) {
                value = splits[1];
            }
            return new Pair<>(splits[0], value);
        }
        return null;
    }

    public static String getUnitId(String outletId, OrderAttribute attribute) {
        return OrderAttribute.NONE.equals(attribute) ? outletId : outletId + "-" + attribute.name();
    }


    public static String getFormattedTime(Date date, String pattern) {
        return new SimpleDateFormat(pattern).format(date);
    }

    public static boolean isSuperComboOrHeroCombo(Integer productSubCategoryId) {
        return productSubCategoryId == 3675 || productSubCategoryId == 3676;
    }

    public static String getDimensionFromSwiggyMenuVariant(String variantName) {
        variantName = variantName.toLowerCase().replace(" ", "");
        if (variantName.contains("miniketli")) {
            return "MiniKetli";
        } else if (variantName.contains("chotiketli")) {
            return "ChotKetli";
        } else if (variantName.contains("badiketli")) {
            return "BadiKetli";
        } else if (variantName.contains("regular")) {
            return "Regular";
        } else if (variantName.contains("full")) {
            return "Full";
        } else if (variantName.contains("none")) {
            return "None";
        }
        return null;
    }

    public static String getZomatoTestOutletId(Integer brandId) {
        if(ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID.equals(brandId) ||
            ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID.equals(brandId)) {
            return ChannelPartnerServiceConstants.TEST_UNIT_NEW;
        } else {
            return ChannelPartnerServiceConstants.TEST_UNIT_DESI_CANTEEN;
        }
    }

    public static String getMagicPinTestOutletId() {
        return ChannelPartnerServiceConstants.MAGIC_PIN_DEV_TEST_OUTLET;
    }

    public static String getSwiggyTestOutletId() {
            return ChannelPartnerServiceConstants.TEST_UNIT_OLD;
    }

    public static void main(String[] args) {
        Pair<String, String> val;
        val = parseUnitId("112200DC-SWIGGY_15M");
        System.out.println(val.getKey() + " - " + val.getValue());
    }

    public static Map<String, String> getOrderItemRemarkMap() {

        Map<String, String> map = new HashMap<>();
        map.put("sugar-free", NO_SUGAR);
        map.put("sugar free", NO_SUGAR);
        map.put("unsweetened", NO_SUGAR);
        map.put("without sugar", NO_SUGAR);
        map.put("no added sugar", NO_SUGAR);
        map.put("sweetener-free", NO_SUGAR);
        map.put("free from sugar", NO_SUGAR);
        map.put("zero sugar", NO_SUGAR);
        map.put("no sugar added", NO_SUGAR);
        map.put("without any sugar", NO_SUGAR);
        map.put("not sweetened", NO_SUGAR);
        map.put("free of sugar", NO_SUGAR);
        map.put("no sugar", NO_SUGAR);
        map.put("diabetes",NO_SUGAR);
        map.put("diabetic", NO_SUGAR);
        map.put("sweetener free", NO_SUGAR);
        map.put("nosugar", NO_SUGAR);
        return map;

    }

    public static List<Integer> getNewChannelPartners(){
        return  List.of(24);
    }

    public static List<String> getNewChannelPartnerNames(){
        return  List.of(ChannelPartnerServiceConstants.MAGICPIN);
    }


    public static PartnerRequestType getPartnerMenuRequestType(HttpServletRequest httpServletRequest){
        String endPoint = httpServletRequest.getRequestURI();
        String[] endpointArr  = endPoint.split("/");
        String path = endpointArr[endpointArr.length-1];
        for(PartnerRequestType requestType : PartnerRequestType.values()){
            if(requestType.getRequestType().equalsIgnoreCase(path)){
                return requestType;
            }
        }
        return null;
    }


    public static String generateID() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString();
    }

    public static Set<Integer> convertCommaSeparatedStringToList(String commaSeparatedString){
        Set<Integer> list = new HashSet<>();
        if(!StringUtils.isEmpty(commaSeparatedString)){
            String[] arr = commaSeparatedString.split(",");
            if(Objects.nonNull(arr) && arr.length > 0) {
                for (String str : arr) {
                    list.add(Integer.valueOf(str));
                }
            }
        }
        return list;
    }

    public static String isPriortizedOrder(List<String> tags , String priotizationKey){
        if(!CollectionUtils.isEmpty(tags)){
            if(tags.contains(priotizationKey)){
                return  AppConstants.YES;
            }
        }
        return AppConstants.NO;
    }

    public static Integer getUnitIdFromStoreId(String storeId){
        return Integer.valueOf(storeId.replaceAll("\\D.*$", ""));
    }

    public static Integer getBrandIdFromStoreId(String storeId){
        String brandCode =  storeId.replaceAll("^\\d+", "");
        switch (brandCode){
            case "GNT" :
                return AppConstants.GNT_BRAND_ID;
            default:
                return AppConstants.CHAAYOS_BRAND_ID;
        }
    }

    @Builder
    public static PartnerOrderFallbackLog buildFallbackLog(
            PartnerOrderDetail detail,
            String action,
            String previousState,
            String comment,
            PartnerOrderFallbackStatus statusLog,
            String actionCategory
            ) {
        return PartnerOrderFallbackLog.builder()
                .orderId(detail.getPartnerOrderId())
                .partnerOrderFallbackStatusId(statusLog)
                .action(action)
                .previousState(previousState)
                .currentStatus(detail.getFallbackProcessedBy() != null ? detail.getFallbackProcessedBy() : null)
                .actionTime(ChannelPartnerUtils.getCurrentTimestamp())
                .actionCategory(actionCategory)
                .comment(comment)
                .employeeId(detail.getFallbackProcessedBy())
                .build();
    }

    @Builder
    public static PartnerOrderFallbackStatus buildFallbackStatusLog(PartnerOrderDetail detail, String finalStatus) {
        return PartnerOrderFallbackStatus.builder()
                .orderId(detail.getPartnerOrderId())
                .employeeId(detail.getFallbackProcessedBy())
                .finalStatus(finalStatus)
                .actionTime(ChannelPartnerUtils.getCurrentTimestamp())
                .cancellationReason(detail.getCancellationReason())
                .customerResponse(detail.getCustomerResponse())
                .partnerResponse(detail.getPartnerResponse())
                .build();
        }

    public static String getBrandName(Integer brandId){
        if(brandId.equals(ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID)){
            return AppConstants.CHAAYOS_BRAND;
        }else if(brandId.equals(ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID)){
            return AppConstants.GNT_BRAND;
        }else if(brandId.equals(A)){
            return "Dohful";
        }
        return null;
    }
}
