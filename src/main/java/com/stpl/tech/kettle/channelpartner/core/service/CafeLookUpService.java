package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.domain.model.CafeStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerOfflineStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCafeStatusRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.ZomatoCafeStatusData;

import java.io.IOException;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.util.List;

public interface CafeLookUpService {

    void getSwiggyCafeStatus() throws URISyntaxException, ChannelPartnerException;

    void getZomatoCafeStatus() throws URISyntaxException, ChannelPartnerException;

    void getSwiggyCafeStatus_() throws URISyntaxException, ChannelPartnerException;
//    public void getZomatoCafeStatus() throws URISyntaxException, ChannelPartnerException;

    public List<ZomatoCafeStatusData> getZomatoCafeStatusData();

    public boolean setZomatoCafeStatusData(ZomatoCafeStatusRequest request) throws ParseException, IOException;

    public CafeStatus getCafeStatusForUnit(Integer unitId, Integer brandId);

    void updateSwiggyCafeStatus(String outletId,int unitId);

    List<UnitPartnerOfflineStatus> getCafesOfflineOnPartner();

    List<UnitPartnerOfflineStatus> getUnitCafesOfflineOnPartner(List<Integer> unitIds);

    void updatePartnerStatusAfterChangingCafeStatus(Integer unitId, Boolean status, Integer brandId, Integer partnerId);

    void unitClosureNotify(Integer integer);
}
