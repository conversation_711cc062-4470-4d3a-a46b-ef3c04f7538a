package com.stpl.tech.kettle.channelpartner.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockOutProductInfo {
    
    private String orderId; // from status table
    private Integer productId;
    private String productName;
    private String dimension;
    private Integer requestedCount;
    private Integer availableCount;
    
}
